import { Input } from "@/components/ui/input";
import { TableCell, TableRow } from "@/components/ui/table";
import { Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import MonthPicker from "./MonthPicker";

interface TableBodyRowProps {
  row: Record<string, any>;
  headers: string[];
  index: number;
  onChange: (i: number, key: string, value: string) => void;
  onDelete: (i: number) => void;
}

export default function TableBodyRow({
  row,
  headers,
  index,
  onChange,
  onDelete,
}: TableBodyRowProps) {
  return (
    <TableRow className="hover:bg-gray-50 dark:hover:bg-neutral-800/80 transition-colors">
      {headers.map((key) => (
        <TableCell key={key} className="p-2 relative">
          {key === "Month" ? (
            <div className="relative z-10">
              <MonthPicker
                value={row[key.toLowerCase()] ?? ""}
                onChange={(val) => onChange(index, key, val)}
              />
            </div>
          ) : (
            <Input
              value={row[key.toLowerCase().replace(/\s/g, "")] ?? ""}
              onChange={(e) => onChange(index, key, e.target.value)}
              className="w-full h-8 text-sm"
              placeholder={`Enter ${key}`}
            />
          )}
        </TableCell>
      ))}

      <TableCell className="text-center w-[50px]">
        <Button
          variant="ghost"
          size="sm"
          className="hover:bg-red-100 dark:hover:bg-red-900/40"
          onClick={() => onDelete(index)}
        >
          <Trash2 className="h-4 w-4 text-red-500" />
        </Button>
      </TableCell>
    </TableRow>
  );
}
