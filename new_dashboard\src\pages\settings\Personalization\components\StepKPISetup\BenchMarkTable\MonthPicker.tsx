"use client";
import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import * as PopoverPrimitive from "@radix-ui/react-popover";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { cn } from "@/utils/index";

interface MonthPickerProps {
  value: string;
  onChange: (val: string) => void;
}

export default function MonthPicker({ value, onChange }: MonthPickerProps) {
  const [open, setOpen] = React.useState(false);
  const [date, setDate] = React.useState<Date | undefined>(
    value ? new Date(value) : undefined
  );

  // Update date when value prop changes
  React.useEffect(() => {
    if (value) {
      setDate(new Date(value));
    } else {
      setDate(undefined);
    }
  }, [value]);

  return (
    <PopoverPrimitive.Root open={open} onOpenChange={setOpen}>
      <PopoverPrimitive.Trigger asChild>
        <Button
          variant="outline"
          className="w-full h-8 text-sm justify-start text-left font-normal relative"
          type="button"
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "MMM yyyy") : "Select month"}
        </Button>
      </PopoverPrimitive.Trigger>

      <PopoverPrimitive.Portal container={document.body}>
        <PopoverPrimitive.Content
          side="bottom"
          align="start"
          sideOffset={8}
          avoidCollisions={true}
          collisionPadding={10}
          style={{
            zIndex: 99999,
            position: 'fixed'
          }}
          className={cn(
            "w-auto p-0 bg-white dark:bg-neutral-900 shadow-xl border border-gray-200 dark:border-neutral-800 rounded-md",
            "data-[state=open]:animate-in data-[state=closed]:animate-out",
            "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
            "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
            "data-[side=bottom]:slide-in-from-top-2",
            "max-h-[300px] overflow-auto"
          )}
        >
          <Calendar
            mode="single"
            selected={date}
            onSelect={(newDate: Date | undefined) => {
              setDate(newDate);
              if (newDate) {
                onChange(format(newDate, "yyyy-MM"));
                setOpen(false);
              }
            }}
            autoFocus
          />
        </PopoverPrimitive.Content>
      </PopoverPrimitive.Portal>
    </PopoverPrimitive.Root>
  );
}
